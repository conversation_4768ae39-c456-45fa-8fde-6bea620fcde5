namespace BCP.Core.Document.Models;

public class AptExportSelectedDocumentsRequest
{
    public int MemberFirmId { get; set; }
    public int ClientId { get; set; }
    public int ProjectId { get; set; }
    public List<int> Files { get; set; } = new();
    public List<int> Folders { get; set; } = new();
}

public class AptExportResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}
