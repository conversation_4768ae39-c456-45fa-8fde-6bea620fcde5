using BCP.Core.BGP.ControlAPI.Models;
using BCP.Core.BGP.ControlAPI.Spec;
using BCP.Core.Common;
using BCP.Core.Document;
using BCP.Core.Document.Models;
using BCP.Core.Modules.BGP.ControlAPI.Models;
using BCP.Core.User;
using Microsoft.AspNetCore.Mvc;

namespace BCP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DocumentController : ControllerBase
    {
        private readonly IControlAPIService _bgpService;
        private readonly IUserContext _contextService;
        private readonly int _memberFirmId;
        private readonly IDocumentService _documentService;
        private static readonly string BaseUrl = "https://gpl-ctrlapi-acc-eur.bdo.global";

        public DocumentController(
            IControlAPIService bgpService,
            IDocumentService documentService,
            IUserContext contextService,
            WebSettings webSettings
        )
        {
            _bgpService = bgpService;
            _documentService = documentService;
            _contextService = contextService;
            _memberFirmId = webSettings.MemberFirmId;
        }

        [HttpPost("upload")]
        // TODO: Need to adjust this for a better approach on uploading large files
        [RequestSizeLimit(2147483648)]
        public async Task<IActionResult> UploadDocument(
            [FromForm] IFormFile file,
            [FromQuery] int projectId,
            [FromQuery] int clientId,
            [FromQuery] string? path,
            [FromQuery] int? folderListItemId
        )
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded");
            }

            if (path == "/")
            {
                path = "";
            }

            string filePath = (string.IsNullOrWhiteSpace(path) ? "" : path) + "/" + file.FileName;

            BDOUploadDocument response = await _documentService.UploadDocumentAsync(
                file,
                _memberFirmId,
                clientId,
                filePath,
                projectId,
                folderListItemId
            );
            return Ok(response);
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateNewFolder([FromBody] FolderCreationRequest request)
        {
            var result = await _documentService.CreateNewFolder(request);

            return Ok(result);
        }

        [HttpPut("manageAccess")]
        public async Task ManageAccess([FromBody] BDOManageAccessRequest request)
        {
            var user = _contextService.CurrentUser;
            request.MemberFirmId = _memberFirmId;
            if (user != null && !request.UserIds.Contains(user.BgpId.ToString()))
            {
                throw new CoreException(
                    CoreError.BadRequest,
                    "Attempting to remove your own access, failed to update document access"
                );
            }
            await _bgpService.UpdateDocumentAccess(request);
        }

        /// <summary>
        ///  Note: The move file action didn't udpate the "ModifiedAt" property of the selected file.
        /// </summary>
        /// <param name="body"></param>
        /// <returns></returns>
        [HttpPost("move")]
        public async Task<IActionResult> MoveDocuments(MoveDocumentsRequest body)
        {
            var user = _contextService.CurrentUser;
            // TODO: Add the security check. Client User should not move documents.
            if (
                user != null
                && body.ReadOnly.GetValueOrDefault()
                && body.IsClient.GetValueOrDefault()
            )
            {
                return StatusCode(400, "User does not have permission to move document");
            }

            var newRequest = new MoveDocumentsRequest
            {
                ItemIds = body.ItemIds,
                DestinationFolderId = body.DestinationFolderId,
                ClientId = body.ClientId,
                ProjectId = body.ProjectId,
                MemberFirmId = body.MemberFirmId,
            };
            try
            {
                var results = await _bgpService.MoveDocuments(newRequest);
                return Ok(results);
            }
            catch (Exception e)
            {
                return StatusCode(400, e.Message);
            }
        }

        [HttpPost("downloadFile")]
        public async Task<IActionResult> CreateDownloadFileAsync(
            [FromBody] DocumentDownloadRequest request
        )
        {
            var downloadKey = await _documentService.CreateDownloadFilesOrFolderAsync(request);
            return Ok(
                new
                {
                    downloadLink = $"{BaseUrl}/api/document/DownloadDocumentsFiles?downloadKey={downloadKey}",
                }
            );
        }

        [HttpGet("downloadFile")]
        public async Task<IActionResult> DownloadFileAsync(string downloadKey)
        {
            var response = await _documentService.DownloadFilesOrFolderAsync(downloadKey);
            return Ok(new { response });
        }

        [HttpGet("folderContent")]
        public async Task<DocumentsResponse?> GetFolderContentAsync(
            string path,
            int clientId,
            int? projectId,
            string? filter
        )
        {
            return await _documentService.GetFolderContentAsync(
                path,
                _memberFirmId,
                clientId,
                projectId,
                filter
            );
        }

        [HttpGet("recycleBin")]
        public async Task<BDORecycleBinDocument[]?> GetRecycleBin(
            [FromQuery] int projectId,
            [FromQuery] int clientId
        )
        {
            return await _documentService.GetRecycleBinAsync(_memberFirmId, clientId, projectId);
        }

        [HttpPost("deleteRecycleBinItemsPermanently")]
        public async Task<IActionResult> DeleteRecycleBinItemsPermanently(
            [FromBody] BDODeleteRecycleBinItemPermanentlyRequest request
        )
        {
            try
            {
                request.MemberFirmId = _memberFirmId;
                var result = await _documentService.DeleteRecycleBinItemsPermanently(request);

                return result
                    ? Ok(new { success = true, message = $"Deleted Files" })
                    : BadRequest(new { success = false, message = $"Failed to delete files" });
            }
            catch (Exception ex)
            {
                return StatusCode(
                    500,
                    new { success = false, message = $"An error occurred: {ex.Message}" }
                );
            }
        }

        [HttpPost("moveDocumentsToRecycleBin")]
        public async Task<IActionResult> MoveDocumentsToRecycleBin(
            [FromBody] BDOMoveToTrashRequest request
        )
        {
            // TODO: Add the security check. Client User should not move documents.
            var user = _contextService.CurrentUser;
            if (user != null && request.ReadOnly.GetValueOrDefault())
            {
                return StatusCode(400, "User does not have permission to move document");
            }

            try
            {
                var newRequest = new BDOMoveToTrashRequest
                {
                    ClientId = request.ClientId,
                    Files = request.Files,
                    Folders = request.Folders,
                    FirmId = request.FirmId,
                    ProjectId = request.ProjectId,
                };
                await _documentService.MoveItemsToRecycleBin(newRequest);
                return Ok(
                    new { success = true, message = $"Files successfully moved to recycle bin" }
                );
            }
            catch (Exception ex)
            {
                return StatusCode(
                    500,
                    new { success = false, message = $"An error occurred: {ex.Message}" }
                );
            }
        }

        [HttpGet(nameof(GetAvailableUsersToAssignAccess))]
        public async Task<ActionResult> GetAvailableUsersToAssignAccess(
            [FromQuery] BDOAvailableUsersToAssignAccessRequest request
        )
        {
            return Ok(await _documentService.GetAvailableUsersToAssignAccess(request));
        }

        [HttpPost("restoreDocuments")]
        public async Task<IActionResult> RestoreDocuments(
            [FromBody] BDORestoreRequest request,
            [FromQuery] int projectId,
            [FromQuery] int clientId
        )
        {
            await _documentService.RestoreDocuments(clientId, _memberFirmId, projectId, request);
            return Ok(new { success = true, message = $"Files successfully restored" });
        }

        [HttpPost("rename")]
        public async Task<IActionResult> RenameDocument([FromBody] BDORenameDocumentRequest request)
        {
            request.MemberFirmId = _memberFirmId;
            var result = await _bgpService.RenameDocument(request);
            return result
                ? Ok(new { success = true, message = $"Renamed document to {request.Name}" })
                : BadRequest(new { success = false, message = $"Failed to  rename document" });
        }

        [HttpPost(nameof(UpdatePermissions))]
        public async Task<IActionResult> UpdatePermissions(
            [FromBody] BDOUpdateFolderPermissionsRequest request
        )
        {
            var result = await _bgpService.UpdateItemPermissions(request);
            return result
                ? Ok(new { success = true, message = $"Updated Permissions" })
                : BadRequest(new { success = false, message = $"Failed to update permissions" });
        }

        [HttpGet(nameof(GetPermissions))]
        public async Task<dynamic> GetPermissions(
            [FromQuery] int projectId,
            [FromQuery] int clientId,
            [FromQuery] int memberFirmId,
            [FromQuery] int itemId
        )
        {
            var result = await _bgpService.GetItemPermissions(
                memberFirmId,
                clientId,
                projectId,
                itemId
            );
            return result;
        }

        [HttpGet(nameof(GetDocumentDetailsByDriveItemId))]
        public async Task<DocumentResponse?> GetDocumentDetailsByDriveItemId(
            [FromQuery] int projectId,
            [FromQuery] int clientId,
            [FromQuery] string driveItemId
        )
        {
            var result = await _documentService.GetDocumentDetails(
                clientId,
                projectId,
                driveItemId,
                null
            );

            return result;
        }

        [HttpPost("apt-export-validation")]
        public async Task<IActionResult> GetAptExportValidation(
            [FromBody] AptExportValidationRequest request
        )
        {
            try
            {
                var result = await _documentService.GetAptExportValidationAsync(
                    _memberFirmId,
                    request.ClientId,
                    request.ProjectId,
                    request.Documents
                );
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = ex.Message });
            }
        }

        [HttpPost("apt-export-all")]
        public async Task<IActionResult> ExportAllDocumentsToApt(
            [FromQuery] int clientId,
            [FromQuery] int projectId
        )
        {
            try
            {
                var result = await _documentService.ExportAllDocumentsToAptAsync(
                    _memberFirmId,
                    clientId,
                    projectId
                );
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = ex.Message });
            }
        }

        [HttpPost("apt-export-selected")]
        public async Task<IActionResult> ExportSelectedDocumentsToApt(
            [FromBody] AptExportSelectedDocumentsRequest request
        )
        {
            try
            {
                var result = await _documentService.ExportSelectedDocumentsToAptAsync(
                    _memberFirmId,
                    request.ClientId,
                    request.ProjectId,
                    request.Files,
                    request.Folders
                );
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = ex.Message });
            }
        }

        [HttpGet(nameof(GetDocumentDetailsByPath))]
        public async Task<DocumentResponse?> GetDocumentDetailsByPath(
            [FromQuery] int projectId,
            [FromQuery] int clientId,
            [FromQuery] string path
        )
        {
            var result = await _documentService.GetDocumentDetails(clientId, projectId, null, path);
            return result;
        }
    }
}
