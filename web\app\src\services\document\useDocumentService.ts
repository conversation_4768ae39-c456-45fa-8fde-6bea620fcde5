import { singletonHook } from "react-singleton-hook";
import { useCallback, useEffect, useState } from "react";
import { snakeCase } from "lodash";
import { useHttpAuthed } from "~/utils/http";
import {
  IDocumentService,
  INodesStore,
  IUploadStore,
  IProjectDriveIdStore,
  IDocumentFoldersStore,
  IMoveToTrashRequest,
  IRenameRequest,
  IPerson,
  INode,
  IUpdatePermission,
  INodePermission,
  MoveDocumentsResponse,
  AptExportValidationResponse,
  AptExportResponse,
} from "./spec";
import { useToastService } from "../toast";
import { useTranslation } from "react-i18next";

const defaultService: IDocumentService = {
  ready: false,
  nodes: null,
  moveNodes: null,
  upload: null,
  projectDriveId: null,
  isCurrentFolderRestricted: null,
  currentFolderListItemId: null,
  fetchNodes: async () => undefined,
  uploadDocument: async (
    _file: File,
    _clientId: number,
    _projectId: number,
    _path: string
  ) => undefined,
  getAvailableUsersToAssignAccess: async (
    _memberFirmId: number,
    _clientId: number,
    _projectId?: number,
    _itemId?: number | undefined,
    _newFolderPath?: string | undefined
  ) => [],
  getDocumentDetailsByDriveItemId: async (
    _clientId: number,
    _projectId: number,
    _driveItemId: string
  ) => <INode>{},
  getDocumentDetailsByPath: async (
    _clientId: number,
    _projectId: number,
    _path: string
  ) => <INode>{},
  getPermissions: async (
    _memberFirmId: number,
    _clientId: number,
    _projectId: number,
    _itemId: number
  ) => <INodePermission>{},
  fetchDriveId: async (
    _memberFirmId: number,
    _clientId: number,
    _projectId: number
  ) => undefined,
  fetchFolderContent: async (
    _memberFirmId: number,
    _clientId: number,
    _path: string,
    _projectId?: number,
    _filter?: string,
    _signal?: AbortSignal
  ) => undefined,
  fetchMoveFolderContent: async (
    _memberFirmId: number,
    _clientId: number,
    _path: string,
    _projectId?: number
  ) => undefined,
  getFolderContentByPath: async (
    _memberFirmId: number,
    _clientId: number,
    _path: string,
    _projectId?: number
  ) => [],
  moveDocuments: async (
    _memberFirmId,
    _clientId,
    _projectId,
    _destinationFolderId,
    _itemIds,
    _readOnly,
    _isClient?
  ) => <MoveDocumentsResponse>{ documents: {} },
  fetchFileDownload: async (_downloadLink: string) => undefined,
  createDocumentsFolder: async (
    _memberFirmId: number,
    _clientId: number,
    _projectId: number,
    _path: string,
    _name: string
  ) => <INode>{},
  updatePermission: async (_data: IUpdatePermission) => undefined,
  moveToTrash: async (_: IMoveToTrashRequest) => undefined,
  createDownloadFileAndFolders: async (
    _memberFirmId: number,
    _clientId: number,
    _projectId: number,
    _files: string[],
    _folders: string[]
  ) => undefined,
  rename: async (_: IRenameRequest) => undefined,
  resetNavData: () => undefined,
  setIsLoading: () => undefined,
  updateIsRestrictedFolder: (_isRestricted: boolean) => undefined,
  updateFolderItemId: (_listItemId: number) => undefined,
  getAptExportValidation: async (
    _memberFirmId: number,
    _clientId: number,
    _projectId: number,
    _documents: Array<{ path: string; isFolder: boolean }>,
    _signal?: AbortSignal
  ) => ({ documents: [] }),

  exportAllDocumentsToApt: async () => ({ success: false, message: "Not implemented" }),

  exportSelectedDocumentsToApt: async () => ({ success: false, message: "Not implemented" }),
};

const useDocumentServiceImpl = (): IDocumentService => {
  const [ready, setReady] = useState(false);
  const [_nodes, _setNodes] = useState<IDocumentService["nodes"]>(null);
  const [nodes, setNodes] = useState<IDocumentService["nodes"]>(null);
  const [_moveNodes, _setMoveNodes] = useState<INodesStore>();
  const [moveNodes, setMoveNodes] = useState<INodesStore>();
  const [upload, setUpload] = useState<IUploadStore>();
  const [, setDocumentFolder] = useState<IDocumentFoldersStore>();
  const [isCurrentFolderRestricted, setIsCurrentFolderRestricted] = useState<
    boolean
  >(false);
  const [currentFolderListItemId, setCurrentFolderListItemId] = useState<
    number | null
  >(null);
  const { showToast } = useToastService();

  const { t } = useTranslation("global");
  const { t: tDocs } = useTranslation("documents");

  const [
    projectDriveId,
    setProjectDriveId,
  ] = useState<IProjectDriveIdStore | null>(null);
  const http = useHttpAuthed();

  const createDocumentsFolder = async (
    memberFirmId: number,
    clientId: number,
    projectId: number,
    path: string,
    name: string
  ): Promise<INode | undefined> => {
    try {
      setDocumentFolder({ isBusy: true });

      const folder = await http.post(`/api/Document/create`, {
        memberFirmId,
        clientId,
        projectId,
        path,
        name,
      });

      setDocumentFolder({ isBusy: false });

      return folder;
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error("Error fetching drive ID:", error);
      setDocumentFolder({ isBusy: false, error });
    }
    return undefined;
  };

  const getAvailableUsersToAssignAccess = async (
    memberFirmId: number,
    clientId: number,
    projectId?: number,
    itemId?: number | undefined,
    newFolderPath?: string | undefined
  ): Promise<IPerson[]> => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params: any = {
      memberFirmId: memberFirmId.toString(),
      clientId: clientId.toString(),
    };
    if (projectId) {
      params.projectId = projectId.toString();
    }
    if (itemId) {
      params.itemId = itemId;
    }
    if (newFolderPath) {
      params.newFolderPath = newFolderPath;
    }
    const response = await http.get(
      `/api/Document/GetAvailableUsersToAssignAccess?` +
        new URLSearchParams(params).toString()
    );
    return response;
  };

  const getPermissions = async (
    memberFirmId: number,
    clientId: number,
    projectId: number,
    itemId: number
  ) => {
    return await http.get(
      `/api/Document/GetPermissions?` +
        new URLSearchParams({
          memberFirmId: memberFirmId.toString(),
          clientId: clientId.toString(),
          projectId: projectId.toString(),
          itemId: itemId.toString(),
        }).toString()
    );
  };

  const fetchDriveId = async (
    memberFirmId: number,
    clientId: number,
    projectId: number
  ): Promise<void> => {
    try {
      setProjectDriveId({ isBusy: true });

      const response = await http.get(
        `/api/Document/driveId?memberFirmId=${memberFirmId}&clientId=${clientId}&projectId=${projectId}`
      );

      if (!response.driveId) {
        setProjectDriveId({
          isBusy: false,
          error: response.error || "Failed to retrieve Drive ID",
        });
        return;
      }

      setProjectDriveId({ isBusy: false, driveId: response.driveId });
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error("Error fetching drive ID:", error);
      setProjectDriveId({ isBusy: false, error });
    }
  };

  const getFolderContent = async (
    memberFirmId: number,
    clientId: number,
    path: string,
    projectId?: number,
    filter?: string,
    signal?: AbortSignal
  ): Promise<INode[]> => {
    setIsLoading(true);
    const response = await http.get(
      `/api/Document/folderContent?memberFirmId=${memberFirmId}&clientId=${clientId}&path=${encodeURIComponent(
        path
      )}${projectId ? `&projectId=${projectId}` : ""}${
        filter ? `&filter=${filter}` : ""
      }`,
      undefined,
      signal
    );

    if (!response.items) {
      showToast({
        message: t("error"),
        type: "error",
        persist: false,
      });
      throw new Error(response.error || "Failed to retrieve folder content");
    }
    setIsLoading(false);
    return response.items;
  };

  const fetchFolderContent = async (
    memberFirmId: number,
    clientId: number,
    path: string,
    projectId?: number,
    filter?: string,
    signal?: AbortSignal
  ): Promise<void> => {
    _setNodes(prev => ({ ...prev, isBusy: true }));

    try {
      setIsLoading(true);
      const items = await getFolderContent(
        memberFirmId,
        clientId,
        path,
        projectId,
        filter,
        signal
      );

      _setNodes(prev => ({
        ...prev,
        data: items,
        error: undefined,
        isBusy: false,
      }));
    } catch (err) {
      if (err instanceof DOMException && err.name === "AbortError") return;

      const error = err instanceof Error ? err.message : "Unknown error";
      _setNodes(prev => ({
        ...prev,
        error,
        isBusy: false,
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const localizeNodes = useCallback(
    (nodes: INode[]) => {
      return nodes.map(node => {
        return localizeNode(node);
      });
    },
    [tDocs]
  );

  const localizeNode = (node: INode): INode => {
    if (node.isTemplated) {
      const key = `folder_${snakeCase(node.name)}`;
      return {
        ...node,
        displayName: tDocs(key, node.name),
      };
    }
    return node;
  };

  const fetchMoveFolderContent = async (
    memberFirmId: number,
    clientId: number,
    path: string,
    projectId?: number
  ): Promise<void> => {
    try {
      _setMoveNodes({ isBusy: true });

      const response = await http.get(
        `/api/Document/folderContent?memberFirmId=${memberFirmId}&clientId=${clientId}&path=${encodeURIComponent(
          path
        )}${projectId ? `&projectId=${projectId}` : ""}`
      );

      if (!response.items) {
        _setMoveNodes({
          error: response.error || "Failed to retrieve folder content",
          isBusy: false,
        });
        return;
      }

      _setMoveNodes({ data: response.items, isBusy: false });
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error("Error fetching folder content:", error);
      _setMoveNodes({ isBusy: false, error });
    }
  };

  useEffect(() => {
    if (!_moveNodes) return;
    const { data, ...rest } = _moveNodes;
    setMoveNodes({
      ...rest,
      data: data ? localizeNodes(data) : data,
    });
  }, [_moveNodes, localizeNodes]);

  const getFolderContentByPath = async (
    memberFirmId: number,
    clientId: number,
    path: string,
    projectId?: number
  ) => {
    try {
      const response = await http.get(
        `/api/Document/folderContent?memberFirmId=${memberFirmId}&clientId=${clientId}&path=${encodeURIComponent(
          path ? path : "/"
        )}${projectId ? `&projectId=${projectId}` : ""}`
      );

      if (!response.items) {
        return response.error;
      }

      return localizeNodes(response.items);
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error("Error fetching folder content:", error);
      return error;
    }
  };

  const moveDocuments = async (
    memberFirmId: number,
    clientId: number,
    projectId: number,
    destinationFolderId: number,
    itemIds: number[],
    readOnly: boolean,
    isClient?: boolean
  ): Promise<MoveDocumentsResponse> => {
    if (
      !memberFirmId ||
      !clientId ||
      !projectId ||
      destinationFolderId == null ||
      itemIds.length === 0
    ) {
      throw new Error(
        "All parameters are required, including at least one item ID."
      );
    }

    const payload = {
      memberFirmId,
      clientId,
      projectId,
      destinationFolderId,
      itemIds,
      readOnly,
      isClient,
    };

    try {
      const response = await http.post("/api/Document/Move", payload);

      if (!response || !response.documents) {
        throw new Error("Unexpected response from moveDocuments");
      }

      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.error("Error moving documents:", errorMessage);
      throw new Error(errorMessage);
    }
  };

  const setIsLoading = (isLoading: boolean) => {
    setReady(!isLoading);
  };

  const updateIsRestrictedFolder = (isRestricted: boolean) => {
    setIsCurrentFolderRestricted(isRestricted);
  };

  const updateFolderItemId = (listItemId: number) => {
    setCurrentFolderListItemId(listItemId);
  };

  const fetchNodes = async (): Promise<void> => {
    try {
      _setNodes({ isBusy: true });
      const response = await http.get("/api/document");
      _setNodes({ data: response.items, isBusy: false });
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      _setNodes({ error, isBusy: false });
      console.error(err);
    }
  };

  useEffect(() => {
    if (!_nodes) return;
    const { data, ...rest } = _nodes;
    setNodes({
      ...rest,
      data: data ? localizeNodes(data) : data,
    });
  }, [_nodes, localizeNodes]);

  const updatePermission = async (data: IUpdatePermission) => {
    await http.post(`/api/Document/UpdatePermissions`, data);
  };

  const uploadDocument = async (
    file: File,
    clientId: number,
    projectId: number,
    path: string,
    actionItemId?: string,
    folderListItemId?: number
  ) => {
    const formData = new FormData();
    formData.append("file", file);

    const params = new URLSearchParams({
      clientId: clientId.toString(),
      projectId: projectId.toString(),
      path: path,
    });

    if (actionItemId) {
      params.append("actionItem", actionItemId);
    }

    if (folderListItemId !== undefined) {
      params.append("folderListItemId", folderListItemId.toString());
    }

    try {
      setUpload({ isBusy: true });
      await http.postFormData(
        `/api/Document/upload?${params.toString()}`,
        formData
      );
      setUpload({ isBusy: false });
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      setUpload({ error, isBusy: false });
      console.error(err);
      throw err;
    }
  };

  // Developer Note: This will likely replace our current solution for file download
  const fetchFileDownload = async (filePath: string): Promise<void> => {
    try {
      const response = await http.get(
        `/api/Document/downloadFile?filePath=${encodeURIComponent(filePath)}`,
        {
          responseType: "blob",
        }
      );

      if (!response) {
        console.error("Failed to download file.");
        return;
      }

      // Create a blob URL and trigger the download
      const blob = new Blob([response], { type: "application/octet-stream" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filePath.split("/").pop() || "downloaded-file";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error("Error downloading file:", error);
    }
  };

  const moveToTrash = async (
    moveRequest: IMoveToTrashRequest
  ): Promise<void> => {
    try {
      const url = "/api/document/moveDocumentsToRecycleBin";
      await http.post(url, moveRequest);
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error(error);
      throw Error(error);
    }
  };

  const createDownloadFileAndFolders = async (
    memberFirmId: number,
    clientId: number,
    projectId: number,
    files: string[],
    folders: string[]
  ): Promise<void> => {
    try {
      const response = await http.post(`/api/Document/downloadFile`, {
        memberFirmId,
        clientId,
        projectId,
        files,
        folders,
      });

      const downloadString: string = `${response.downloadLink}`;

      const link = document.createElement("a");
      link.href = downloadString;
      link.target = "_blank";
      link.click();
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error("Error fetching drive ID:", error);
      throw error;
    }
  };

  const rename = async (payload: IRenameRequest): Promise<void> => {
    try {
      const response = await http.post("/api/Document/Rename", payload);

      if (response.error) {
        throw new Error(`Failed to rename documents: ${response.error}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.error("Error renaming document:", errorMessage);
      throw new Error(errorMessage);
    }
  };

  const resetNavData = () => {
    _setNodes({
      data: [],
      isBusy: false,
    });
  };

  const getDocumentDetailsByDriveItemId = useCallback(
    async (clientId: number, projectId: number, driveItemId: string) => {
      try {
        const response = await http.get(
          `/api/Document/getDocumentDetailsByDriveItemId?clientId=${clientId}&projectId=${projectId}&driveItemId=${driveItemId}`
        );

        //if no document details found, return null
        if (!response) {
          console.error("Error fetching document details");
          return null;
        }

        return localizeNode(response);
      } catch (err) {
        const error = err instanceof Error ? err.message : "Unknown error";
        console.error("Error fetching document details:", error);
        return null;
      }
    },
    [http]
  );

  const getDocumentDetailsByPath = useCallback(
    async (clientId: number, projectId: number, path: string) => {
      try {
        const response = await http.get(
          `/api/Document/getDocumentDetailsByPath?clientId=${clientId}&projectId=${projectId}&path=${path}`
        );

        //if no document details found, return null
        if (!response) {
          console.error("Error fetching document details");
          return null;
        }

        return localizeNode(response);
      } catch (err) {
        const error = err instanceof Error ? err.message : "Unknown error";
        console.error("Error fetching document details:", error);
        return null;
      }
    },
    [http]
  );

  const getAptExportValidation = async (
    memberFirmId: number,
    clientId: number,
    projectId: number,
    documents: Array<{ path: string; isFolder: boolean }>,
    signal?: AbortSignal
  ) => {
    try {
      const response = await http.post(
        "/api/Document/apt-export-validation",
        {
          clientId,
          projectId,
          documents,
        },
        undefined,
        signal
      );
      return response as AptExportValidationResponse;
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error("Error getting APT export validation:", error);
      throw error;
    }
  };

  const exportAllDocumentsToApt = async (
    memberFirmId: number,
    clientId: number,
    projectId: number
  ) => {
    try {
      // Call BGP API directly for exporting all documents
      await http.post(
        `https://api.bdo.global/apt-api-acc-can/api/Projects/SendDocumentsToApt?memberFirmId=${memberFirmId}&clientId=${clientId}&projectId=${projectId}`,
        {}
      );
      return {
        success: true,
        message: "All documents exported to APT successfully"
      } as AptExportResponse;
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error("Error exporting all documents to APT:", error);
      throw new Error(error);
    }
  };

  const exportSelectedDocumentsToApt = async (
    memberFirmId: number,
    clientId: number,
    projectId: number,
    files: number[],
    folders: number[]
  ) => {
    try {
      // Call BGP API directly for exporting selected documents
      await http.post(
        "https://api.bdo.global/apt-api-acc-can/api/Projects/SendSelectedDocumentsToApt",
        {
          memberFirmId,
          clientId,
          projectId,
          files,
          folders,
        }
      );
      return {
        success: true,
        message: `Successfully exported ${files.length + folders.length} items to APT`
      } as AptExportResponse;
    } catch (err) {
      const error = err instanceof Error ? err.message : "Unknown error";
      console.error("Error exporting selected documents to APT:", error);
      throw new Error(error);
    }
  };

  return {
    ready,
    nodes,
    moveNodes,
    upload,
    projectDriveId,
    fetchNodes,
    uploadDocument,
    fetchDriveId,
    fetchFolderContent,
    fetchMoveFolderContent,
    getDocumentDetailsByDriveItemId,
    getDocumentDetailsByPath,
    getFolderContentByPath,
    fetchFileDownload,
    moveDocuments,
    createDocumentsFolder,
    moveToTrash,
    createDownloadFileAndFolders,
    rename,
    resetNavData,
    getAvailableUsersToAssignAccess,
    updatePermission,
    getPermissions,
    setIsLoading,
    isCurrentFolderRestricted,
    updateIsRestrictedFolder,
    currentFolderListItemId,
    updateFolderItemId,
    getAptExportValidation,
    exportAllDocumentsToApt,
    exportSelectedDocumentsToApt,
  };
};

export const useDocumentService = singletonHook<IDocumentService>(
  defaultService,
  useDocumentServiceImpl
);
