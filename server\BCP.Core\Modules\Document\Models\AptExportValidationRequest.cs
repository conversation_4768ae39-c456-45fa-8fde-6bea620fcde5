namespace BCP.Core.Document.Models;

public class AptExportValidationRequest
{
    public int ClientId { get; set; }
    public int ProjectId { get; set; }
    public List<AptExportDocumentInfo> Documents { get; set; } = new();
}

public class AptExportDocumentInfo
{
    public string Path { get; set; } = string.Empty;
    public bool IsFolder { get; set; }
}

public class AptExportValidationResponse
{
    public List<AptExportDocumentValidation> Documents { get; set; } = new();
}

public class AptExportDocumentValidation
{
    public string Path { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // "File" or "Folder"
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public AptExportRecursiveCounts? RecursiveCounts { get; set; }
}

public class AptExportRecursiveCounts
{
    public int TotalFiles { get; set; }
    public int TotalSubfolders { get; set; }
    public List<string> InvalidFiles { get; set; } = new(); // Paths of invalid files found recursively
}
