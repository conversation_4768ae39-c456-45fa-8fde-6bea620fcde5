import {
  ButtonSizeEnum,
  ButtonTypeEnum,
  FooterButtonAlignment,
  Icon,
  Modal,
  ModalSize,
  Spinner,
  SpinnerSize,
} from "@bcp/uikit";
import { DocumentRow, iconNameMapping } from "../spec";
import { generateFolderCountText, normalizeActionItemFileType, getPathFromNode, validateDocumentForAptExport } from "../utils";
import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useDocumentService, AptExportDocumentValidation } from "~/services/document";
import styles from "./aptExportModal.module.css";



interface AptExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: () => void;
  onError?: (error: string) => void;
  selectedDocuments: DocumentRow[];
  isExporting?: boolean;
  memberFirmId: number;
  clientId: number;
  projectId: number;
}

export const AptExportModal = ({
  isOpen,
  onClose,
  onExport,
  onError,
  selectedDocuments,
  isExporting = false,
  memberFirmId,
  clientId,
  projectId,
}: AptExportModalProps) => {
  const { t } = useTranslation("documents");
  const { getAptExportValidation, exportSelectedDocumentsToApt } = useDocumentService();

  const [validatedDocuments, setValidatedDocuments] = useState<
    AptExportDocumentValidation[]
  >([]);
  const [isLoadingRecursiveData, setIsLoadingRecursiveData] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Clear state when modal closes, no documents selected, or selection changes
  useEffect(() => {
    if (!isOpen || selectedDocuments.length === 0) {
      // Cancel any ongoing API call
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
      setValidatedDocuments([]);
      setIsLoadingRecursiveData(false);
    }
  }, [isOpen, selectedDocuments.length]);

  // Clear state and cancel previous API call when the actual selection changes
  useEffect(() => {
    if (isOpen && selectedDocuments.length > 0) {
      // Cancel any ongoing API call
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
      setValidatedDocuments([]);
    }
  }, [selectedDocuments, isOpen]);

  useEffect(() => {
    if (isOpen && selectedDocuments.length > 0) {
      const processDocuments = async () => {
        // Cancel any previous API call
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }

        // Create new AbortController for this API call
        const abortController = new AbortController();
        abortControllerRef.current = abortController;

        setIsLoadingRecursiveData(true);

        try {
          // Extract paths and folder info from selected documents
          const documents = selectedDocuments.map(doc => ({
            path: getPathFromNode({ url: doc.url }),
            isFolder: doc.folderOrFile === 'Folder'
          }));

          // Call backend API for validation
          const response = await getAptExportValidation(memberFirmId, clientId, projectId, documents, abortController.signal);

          // Only update state if this request wasn't aborted
          if (!abortController.signal.aborted) {
            setValidatedDocuments(response.documents);
          }
        } catch (error) {
          // Only handle error if this request wasn't aborted
          if (!abortController.signal.aborted) {
            console.error('Error validating documents for APT export:', error);
            // Fallback to frontend validation if backend fails
            const fallbackValidation = selectedDocuments.map(doc => {
              const frontendValidation = validateDocumentForAptExport(doc);
              return {
                path: getPathFromNode({ url: doc.url }),
                name: doc.title,
                type: doc.folderOrFile,
                isValid: frontendValidation.isValid,
                errors: frontendValidation.errors.map(err => err.message),
                recursiveCounts: doc.folderOrFile === 'Folder' ? {
                  totalFiles: doc.fileCount || 0,
                  totalSubfolders: doc.subfolderCount || 0,
                  invalidFiles: []
                } : undefined
              };
            });
            setValidatedDocuments(fallbackValidation);
          }
        } finally {
          // Only update loading state if this request wasn't aborted
          if (!abortController.signal.aborted) {
            setIsLoadingRecursiveData(false);
          }
        }
      };

      processDocuments();
    }
  }, [isOpen, selectedDocuments, getAptExportValidation, memberFirmId, clientId, projectId]);

  // Cleanup: cancel any ongoing API call when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);

  const handleRemoveDocument = (documentPath: string) => {
    setValidatedDocuments(prev => prev.filter(doc => doc.path !== documentPath));
  };

  const handleExport = async () => {
    try {
      // Get only valid documents
      const validDocuments = selectedDocuments.filter(doc =>
        validatedDocuments.some(validated =>
          getPathFromNode({ url: doc.url }) === validated.path && validated.isValid
        )
      );

      // Separate files and folders by their listItemId
      const files: number[] = [];
      const folders: number[] = [];

      validDocuments.forEach(doc => {
        if (doc.folderOrFile === 'Folder') {
          folders.push(doc.listItemId);
        } else {
          files.push(doc.listItemId);
        }
      });

      // Call the real APT export API
      const response = await exportSelectedDocumentsToApt(
        memberFirmId,
        clientId,
        projectId,
        files,
        folders
      );

      if (response.success) {
        // Call the original onExport callback for UI updates (success toast, etc.)
        onExport();
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Error exporting to APT:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      if (onError) {
        onError(errorMessage);
      }
      throw error;
    }
  };

  const hasErrors = validatedDocuments.some(doc => !doc.isValid);
  const canExport = validatedDocuments.length > 0 && !hasErrors && !isExporting && !isLoadingRecursiveData;

  const getFileIcon = (document: AptExportDocumentValidation) => {
    if (document.type === "Folder") {
      return "folder-icon-filled";
    }
    const fileExtension = document.name.split('.').pop()?.toLowerCase() || '';
    const normalizedFileType = normalizeActionItemFileType(fileExtension);
    return iconNameMapping[normalizedFileType];
  };

  const getFileTypeDisplay = (document: AptExportDocumentValidation) => {
    if (document.type === "Folder") {
      // Use recursive counts from backend
      if (document.recursiveCounts) {
        return generateFolderCountText(
          document.recursiveCounts.totalFiles,
          document.recursiveCounts.totalSubfolders,
          t
        );
      }

      // Fallback: try to find the original document data for counts
      const originalDoc = selectedDocuments.find(doc =>
        getPathFromNode({ url: doc.url }) === document.path
      );

      if (originalDoc && (originalDoc.fileCount !== undefined || originalDoc.subfolderCount !== undefined)) {
        return generateFolderCountText(
          originalDoc.fileCount || 0,
          originalDoc.subfolderCount || 0,
          t
        );
      }

      return t("apt-export-folder");
    }
    const fileExtension = document.name.split('.').pop()?.toUpperCase() || '';
    return fileExtension || t("apt-export-unknown-file-type");
  };

  return (
    <Modal
      isVisible={isOpen}
      hide={onClose}
      size={ModalSize.LARGE}
      title={t("apt-export-modal-title")}
      footerBtnAlignment={FooterButtonAlignment.END}
      primaryBtnConfig={{
        id: "export-button",
        label: t("apt-export-button"),
        type: ButtonTypeEnum.primary,
        withRightIcon: false,
        size: ButtonSizeEnum.large,
        onClick: handleExport,
        disabled: !canExport,
        loading: isExporting,
      }}
      secondaryBtnConfig={{
        id: "cancel-button",
        label: t("cancel"),
        withRightIcon: false,
        type: ButtonTypeEnum.secondary,
        size: ButtonSizeEnum.large,
        onClick: onClose,
        disabled: isExporting,
      }}
      allowOverflow={true}
      closeOnInteractOutside={false}
    >
      <div className={styles.description}>
        <p>{t("apt-export-description")}</p>
        <ul>
          <li>
            {t("apt-export-folder-structure-info")}
          </li>
          <li>
            {t("apt-export-subfolder-info")}
          </li>
        </ul>
      </div>

      {isLoadingRecursiveData && (
        <div className={styles.loadingState}>
          <Spinner isLoading={true} size={SpinnerSize.LARGE} />
        </div>
      )}

      <div className={styles.documentsList}>
        {validatedDocuments.map(document => (
          <div
            key={document.path}
            className={`${styles.documentItem} ${
              !document.isValid ? styles.hasError : ""
            }`}
          >
            <div className={styles.documentInfo}>
              <div>
                <Icon iconName={getFileIcon(document)} size={16} />
              </div>
              <div className={styles.documentDetails}>
                <span className={styles.documentName}>
                  {document.name}
                </span>
                <span className={styles.documentMeta}>
                  {getFileTypeDisplay(document)}
                </span>
              </div>
            </div>

            {document.errors.length > 0 && (
              <div className={styles.errorMessages}>
                {document.errors.map((error, index) => (
                  <div key={index} className={styles.errorMessage}>
                    <Icon iconName="warning-icon" size={14} />
                    <span>{error}</span>
                  </div>
                ))}
              </div>
            )}

            <button
              className={styles.removeButton}
              onClick={() => handleRemoveDocument(document.path)}
              title={t("apt-export-remove-tooltip")}
            >
              <Icon iconName="dismiss-icon" size={16} />
            </button>
          </div>
        ))}
      </div>
      {validatedDocuments.length === 0 && !isLoadingRecursiveData && (
        <div className={styles.emptyState}>
          <p>{t("apt-export-empty-state")}</p>
        </div>
      )}
    </Modal>
  );
};
