using BCP.Core.BGP.ControlAPI.Models;
using BCP.Core.Document.Models;
using BCP.Core.Modules.BGP.ControlAPI.Models;
using BCP.Core.Project;
using BCP.Core.User;
using Microsoft.AspNetCore.Http;

namespace BCP.Core.BGP.ControlAPI.Spec
{
    public interface IControlAPIService
    {
        void UseServiceWithToken(string token);
        Task<BDOUser> GetUserAsync();
        Task<dynamic> UpdateMyUserProfile(BDOUpdateMyUserProfileRequest request);
        Task<BDOAccessResponse> GetAccess(int? clientId);
        Task<BDOClients[]> GetClientsAsync();
        Task<BDOClientContact[]> GetClientContactsAsync(int clientId);
        Task<object?> GetClientProfilePictureAsync(Guid uniqueUserId);
        Task<BDOProject[]> GetProjectsAsync();
        Task<BDOClientProjectResponse[]> GetProjectsByClientAsync(int memberFirmId, int clientId);
        Task<BDOProject?> GetProjectByIdAsync(int projectId);

        Task<BDOUser[]?> SearchClientUsersAsync(
            string searchQuery,
            string? clientId = null,
            string? memberFirmId = null
        );

        Task<BDODocuments?> GetFolderContentAsync(
            string path,
            int memberFirmId,
            int clientId,
            int? projectId,
            string? filter,
            string orderKey = "filename",
            string order = "Asc"
        );

        Task<BDOUploadDocument> UploadDocumentAsync(
            IFormFile file,
            int memberFirmId,
            int clientId,
            string path,
            int? projectId
        );

        Task<BDOUploadResult> UploadDocumentToProjectAsync(
            IFormFile file,
            int memberFirmId,
            int clientId,
            string path,
            int projectId,
            Guid taskId
        );

        Task<bool> UploadUploadedDocumentAsync(BDOUploadDocumentRequest request);
        Task<WelcomeMessage?> GetWelcomeMessageAsync(int memberFirmId, string culture);
        Task<MoveDocumentsResponse> MoveDocuments(MoveDocumentsRequest request);
        Task<BDOAddUsersResponse> AddUsers(BDOAddUsersRequest request);
        Task DeliverTaskAsync(BDODeliverTaskRequest request);
        Task<bool> MarkActionItemAsPriority(BDOActionItemPriorityRequest request);
        Task<bool> UnMarkActionItemAsPriority(BDOActionItemPriorityRequest request);
        Task<bool> ResendEmailInvitationAsync(BDOReinviteUser request);
        Task<BDOUser[]> GetProjectUsers(int clientId, int projectId);

        Task<BDOPerson[]> GetAvailableUsersToAssignAccessForDocument(
            BDOAvailableUsersToAssignAccessRequest request
        );
        Task<BDOClientProjectResponse[]> GetClientProjectsAsync(int memberFirmId, int clientId);
        Task<BDOProjectMember[]?> GetProjectMembersAsync(
            int memberFirmId,
            int clientId,
            int projectId
        );
        Task<BDOPortalMember[]?> GetPortalUsers(int memberFirmId, int clientId);
        Task<BDOActionItem[]> GetClientActionItems(int memberFirmId, int clientId);
        Task<IEnumerable<BDOClientProjectResponse>> GetUserAccessForProjectsAsync(
            GetUserAccessForProjectsRequest request
        );
        Task<bool> EditUserAsync(EditUserRequest request);
        Task<Guid> AddCommentsToActionItem(BDOAddCommentRequest request);
        Task<bool> UpdateComment(BDOUpdateCommentRequest request);
        Task<bool> DeleteComment(BDODeleteCommentRequest request);
        Task<BDOComment[]> GetComments(
            int memberFirmId,
            int clientId,
            int projectId,
            string taskId
        );
        Task UpdateActionItem(BDOUpdateActionItemRequest request);
        Task<BDOFolder> CreateNewFolder(FolderCreationRequest request);
        Task<Guid> CreateActionItem(BDOActionItemRequest payload);

        Task<BDOActionItem?> GetActionItem(Guid taskId, int clientId, int projectId);

        Task<bool> DeleteActionItem(Guid taskId, int memberFirmId, int clientId, int projectId);

        Task<BDODocumentDirectory?> GetDocumentsDirectoryAsync(
            int memberFirmId,
            int clientId,
            int projectId,
            string relativePath
        );

        Task<BDODocumentItem?> GetDocumentDetails(
            int clientId,
            int projectId,
            string? driveItemId,
            string? path
        );

        Task<BDOActionItemOverview[]> GetActionItemOverview(int memberFirmId, int clientId);
        Task<bool> CopyDocumentToTask(CopyDocumentToActionItemRequest body);

        Task<DocumentDirectoryResponse> GetDocumentDirectory(
            string path,
            int memberFirmId,
            int clientId,
            int projectId
        );

        Task<bool> CreateUploadsFolder(Guid taskId, int memberFirmId, int clientId, int projectId);

        Task UpdateDocumentAccess(BDOManageAccessRequest request);
        Task<byte[]> DownloadFileAsync(string filePath);
        Task<string> CreateDownloadFilesOrFolderAsync(DocumentDownloadRequest request);
        Task<Stream> DownloadFilesOrFolderAsync(string DownloadKey);

        Task CompleteActionItem(BDOCompleteActionItemRequest request);
        Task<bool> ReturnActionItem(BDOReturnActionItemRequest request);

        Task<ProjectNameValidationResult> ValidateClientProjectName(
            string projectName,
            string siteType
        );
        Task<string> CheckProjectSiteExists(
            int memberFirmId,
            int clientId,
            string projectName,
            bool confidential,
            bool prospect,
            bool isInternal
        );
        Task<bool> RenameProject(int memberFirmId, int clientId, int projectId, string projectName);
        Task<BDORecycleBinDocument[]?> GetRecycleBinAsync(
            int memberFirmId,
            int clientId,
            int projectId
        );
        Task<bool> DeleteRecycleBinItemsPermanently(
            BDODeleteRecycleBinItemPermanentlyRequest request
        );

        Task MoveItemsToRecycleBin(BDOMoveToTrashRequest request);

        Task RestoreDocuments(
            int _clientId,
            int _memberFirmId,
            int _projectId,
            BDORestoreRequest request
        );
        Task<BDOTaskDetailsForDocumentViewResponse?> GetTaskDetailsForDocument(
            int memberFirmId,
            int clientId,
            int projectId
        );

        Task<BDOTaskGroup[]> GetTaskGroups(int memberFirmId, int clientId, int projectId);
        Task<Guid> CreateTaskGroup(CreateTaskGroupRequest request);
        Task<bool> OnSignatureDocumentsUploaded(BDOAssociateDocumentToActionItemRequest request);
        Task<string> SignActionItem(BDOSignActionItemRequest request);
        Task<bool> RenameDocument(BDORenameDocumentRequest request);
        Task<BDOCurrentUserRoles?> GetCurrentUserRoles(GetCurrentUserRolesRequest request);

        Task RemoveUserRoles(RemoveBgpAccessRequest request);

        Task AddUserRoles(AddBgpAccessRequest request);
        Task<BDOPeopleSearchResponse[]> PeopleSearch(
            string query,
            string? clientId = null,
            string? accessType = null
        );

        Task ExportAllDocumentsToApt(int memberFirmId, int clientId, int projectId);

        Task ExportSelectedDocumentsToApt(
            int memberFirmId,
            int clientId,
            int projectId,
            List<int> files,
            List<int> folders
        );
        Task<BDOUser?> GetBdoUserByEmail(string email, int clientId);
        Task<BDOGetAccessibleProjectsResponse?> GetAccessibleProjects(int clientId);
        Task<bool> UpdateItemPermissions(BDOUpdateFolderPermissionsRequest request);
        Task<dynamic> GetItemPermissions(int memberFirmId, int clientId, int projectId, int itemId);
        Task<BDOSearchUser[]> GetUsersFromSiteGroup(int memberFirmId, int clientId, int projectId);
        Task<BDOSearchUser[]> GetMemberFirmUsers(int memberFirmId);
    }
}
