using System.Net.Http.Headers;
using System.Text.Json;
using BCP.Core.BGP.ControlAPI.Config;
using BCP.Core.BGP.ControlAPI.Models;
using BCP.Core.BGP.ControlAPI.Spec;
using BCP.Core.Common;
using BCP.Core.Document.Models;
using BCP.Core.Modules.BGP.ControlAPI.Models;
using BCP.Core.Project;
using BCP.Core.User;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;

namespace BCP.Core.BGP.ControlAPI
{
    public class ControlAPIService : IControlAPIService
    {
        private readonly IControlApiClient _controlApiClient;
        private readonly IControlApiConfig _controlApiConfig;
        private readonly ILogger<ControlAPIService> _logger;

        public ControlAPIService(
            IControlApiClient controlApiClient,
            IControlApiConfig controlApiConfig,
            ILogger<ControlAPIService> logger
        )
        {
            _controlApiClient =
                controlApiClient ?? throw new ArgumentNullException(nameof(controlApiClient));
            _controlApiConfig = controlApiConfig;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public void UseServiceWithToken(string token)
        {
            if (token != null)
            {
                _controlApiClient.InitializeWithToken(token);
            }
        }

        public async Task<BDOUser> GetUserAsync()
        {
            var url = "/api/site/GetContext?firmId=" + _controlApiConfig.MemberFirmId;
            var result = await _controlApiClient.GetAsync<JsonElement?>(url);

            if (
                result.HasValue
                && result.Value.TryGetProperty("currentUser", out JsonElement currentUser)
            )
            {
                BDOUser? user;
                string userJSON;
                try
                {
                    userJSON = currentUser.GetRawText();
                    user = JsonSerializer.Deserialize<BDOUser>(
                        userJSON,
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
                    );
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Failed to serialize user");
                    throw new Exception("Failed to deserialize user", ex);
                }

                if (user is null)
                {
                    _logger.LogError("User deserialized is null");
                    _logger.LogInformation(userJSON);
                    throw new Exception("User deserialized is null");
                }

                if (string.IsNullOrWhiteSpace(user.EmailAddress))
                {
                    _logger.LogError("User does not have an email address");
                    _logger.LogInformation(userJSON);
                    throw new Exception("User does not have an email address");
                }

                return user;
            }

            throw new Exception("User Not Found");
        }

        public async Task<BDOAccessResponse> GetAccess(int? clientId)
        {
            var url = "/api/site/GetContext?firmId=" + _controlApiConfig.MemberFirmId;
            if (clientId.HasValue)
            {
                url += "&clientId=" + clientId.Value;
            }
            var result = await _controlApiClient.GetAsync<JsonElement?>(url);
            var response = new BDOAccessResponse();
            if (
                result.HasValue
                && result.Value.TryGetProperty("projectActions", out JsonElement projectActions)
            )
            {
                string projectActionsJSON;
                try
                {
                    projectActionsJSON = projectActions.GetRawText();
                    var projectIds = JsonSerializer
                        .Deserialize<Dictionary<string, dynamic>>(
                            projectActionsJSON,
                            new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
                        )
                        ?.Keys.Select(p => int.Parse(p))
                        .ToArray();
                    response.ProjectIds = projectIds;
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Failed to serialize project ids");
                    throw new Exception("Failed to serialize project ids", ex);
                }
            }
            if (
                result.HasValue
                && result.Value.TryGetProperty("currentUser", out JsonElement currentUser)
            )
            {
                BDOUser? bdoUser;
                string userJSON;
                try
                {
                    userJSON = currentUser.GetRawText();
                    bdoUser = JsonSerializer.Deserialize<BDOUser>(
                        userJSON,
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
                    );
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Failed to serialize user");
                    throw new Exception("Failed to deserialize user", ex);
                }

                if (bdoUser is null)
                {
                    _logger.LogError("User deserialized is null");
                    _logger.LogInformation(userJSON);
                    throw new Exception("User deserialized is null");
                }

                if (string.IsNullOrWhiteSpace(bdoUser.EmailAddress))
                {
                    _logger.LogError("User does not have an email address");
                    _logger.LogInformation(userJSON);
                    throw new Exception("User does not have an email address");
                }
                response.User = bdoUser;
            }

            return response;
        }

        public async Task<BDOClientContact[]> GetClientContactsAsync(int clientId)
        {
            var firmId = _controlApiConfig.MemberFirmId;
            var url = $"/api/contacts/GetPortalContacts?firmId={firmId}&clientId={clientId}";
            var response = await _controlApiClient.GetAsync<BDOClientContactsResponse>(url);

            return response?.Users ?? Array.Empty<BDOClientContact>();
        }

        public async Task<object?> GetClientProfilePictureAsync(Guid uniqueUserId)
        {
            var url = "/api/UserProfile/GetUserProfilePicture";
            var queryParameters = new Dictionary<string, StringValues>
            {
                { "uniqueUserId", uniqueUserId.ToString() },
                { "pictureSize", "Small" },
            };
            try
            {
                return await _controlApiClient.GetAsync<object?>(url, queryParameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get profile picture");
                return null;
            }
        }

        public async Task<MoveDocumentsResponse> MoveDocuments(MoveDocumentsRequest request)
        {
            var url = "/api/Document/Move";
            return await _controlApiClient.PostAsync<MoveDocumentsResponse>(url, request);
        }

        public async Task<BDOClients[]> GetClientsAsync()
        {
            var url = "/api/ClientSite/GetMyPortals?search=&top=1000";

            return await _controlApiClient.GetAsync<BDOClients[]>(url) ?? Array.Empty<BDOClients>();
        }

        public async Task<BDOProject[]> GetProjectsAsync()
        {
            // TODO Removed as temporarily hot fix to allow access to CDE for users with more than 30 projects
            var queryParams = new Dictionary<string, StringValues> { { "top", "3000" } };
            return await _controlApiClient.GetAsync<BDOProject[]>(
                    "api/Projects/SearchProjects",
                    queryParams
                ) ?? Array.Empty<BDOProject>();
        }

        public async Task<BDOClientProjectResponse[]> GetProjectsByClientAsync(
            int memberFirmId,
            int clientId
        )
        {
            var queryParams = new Dictionary<string, StringValues>
            {
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
            };
            return await _controlApiClient.GetAsync<BDOClientProjectResponse[]>(
                    "api/Projects/GetProjects",
                    queryParams
                ) ?? Array.Empty<BDOClientProjectResponse>();
        }

        public async Task<BDOProject?> GetProjectByIdAsync(int projectId)
        {
            var queryParams = new Dictionary<string, StringValues>
            {
                { "projectId", projectId.ToString() },
                { "top", "1" },
            };
            return await _controlApiClient.GetAsync<BDOProject>(
                "api/Projects/SearchProjects",
                queryParams
            );
        }

        public async Task<byte[]> DownloadFileAsync(string filePath)
        {
            var url = "/api/Application/DownloadFile";
            var queryParams = new Dictionary<string, StringValues> { { "filePath", filePath } };

            var fileData = await _controlApiClient.GetAsync<byte[]>(url, queryParams);

            return fileData ?? throw new InvalidOperationException("File download failed");
        }

        public async Task<string> CreateDownloadFilesOrFolderAsync(DocumentDownloadRequest request)
        {
            var url = "api/Document/CreateDocumentsDownload";

            var payload = new
            {
                request.MemberFirmId,
                request.ClientId,
                request.ProjectId,
                request.Files,
                request.Folders,
            };

            return await _controlApiClient.PostAsync<string>(url, payload);
        }

        public async Task<Stream> DownloadFilesOrFolderAsync(string downloadKey)
        {
            var url = "/api/Document/DownloadDocumentsFiles";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "downloadKey", downloadKey },
            };

            return await _controlApiClient.GetAsync<Stream>(url, queryParams);
        }

        public async Task<BDOUser[]?> SearchClientUsersAsync(
            string searchQuery,
            string? clientId = null,
            string? memberFirmId = null
        )
        {
            var baseUrl = "/api/ClientManagement/SearchClientUsers";
            var queryParams = new Dictionary<string, StringValues> { { "query", searchQuery } };

            if (!string.IsNullOrWhiteSpace(clientId))
            {
                queryParams.Add("clientId", clientId);
            }

            if (!string.IsNullOrWhiteSpace(memberFirmId))
            {
                queryParams.Add("memberFirmId", memberFirmId);
            }

            var url = QueryHelpers.AddQueryString(baseUrl, queryParams);
            return await _controlApiClient.GetAsync<BDOUser[]>(url); //TODO: returns ClientUsers NOT Clients - should this be changed?
        }

        public async Task<BDODocuments?> GetFolderContentAsync(
            string path,
            int memberFirmId,
            int clientId,
            int? projectId,
            string? filter,
            string orderKey = "filename",
            string order = "Asc"
        )
        {
            var baseUrl = "/api/Document/GetFolderContent";

            var queryParams = new Dictionary<string, StringValues>
            {
                { "path", path },
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
            };

            if (projectId.HasValue)
            {
                queryParams.Add("projectId", projectId.Value.ToString());
            }

            if (!string.IsNullOrEmpty(filter))
            {
                queryParams["filter"] = filter;
            }

            var url = QueryHelpers.AddQueryString(baseUrl, queryParams);

            var payload = new { orderKey, order };

            return await _controlApiClient.PostAsync<BDODocuments>(url, payload);
        }

        public async Task<BDOUploadDocument> UploadDocumentAsync(
            IFormFile file,
            int memberFirmId,
            int clientId,
            string path,
            int? projectId
        )
        {
            var url = "/api/document/UploadDocument";

            var content = new MultipartFormDataContent
            {
                { new StringContent(memberFirmId.ToString()), "memberFirmId" },
                { new StringContent(clientId.ToString()), "clientId" },
                { new StringContent(path), "path" },
                { new StringContent(file.Length.ToString()), "fileSize" },
            };

            if (projectId != null)
            {
                content.Add(new StringContent(projectId.ToString()), "projectId");
            }

            using (var stream = file.OpenReadStream())
            {
                var fileContent = new StreamContent(stream)
                {
                    Headers = { ContentType = new MediaTypeHeaderValue(file.ContentType) },
                };
                content.Add(fileContent, "file", file.FileName);
                var response = await _controlApiClient.UploadFileAsync<object[]>(url, content);
                string json = await response.Content.ReadAsStringAsync();
                var bdoUploadDocument = JsonSerializer.Deserialize<BDOUploadDocument>(
                    json,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
                );
                return bdoUploadDocument;
            }
        }

        public async Task DeliverTaskAsync(BDODeliverTaskRequest request)
        {
            var url = "/api/Tasks/DeliverTask";
            await _controlApiClient.PostAsync<HttpResponseMessage>(url, request);
        }

        public async Task<BDOAddUsersResponse> AddUsers(BDOAddUsersRequest request)
        {
            if (!request.Users.Any())
                throw new CoreException(CoreError.BadRequest, "No users found in request");

            request.FirmId = _controlApiConfig.MemberFirmId;
            const string url = "/api/TeamManagement/AddUsers";
            if (request == null)
                throw new ArgumentNullException(nameof(request), "Request cannot be null.");

            return await _controlApiClient.PostAsync<BDOAddUsersResponse>(url, request) ?? [];
        }

        public async Task<BDOUploadResult> UploadDocumentToProjectAsync(
            IFormFile file,
            int memberFirmId,
            int clientId,
            string path,
            int projectId,
            Guid taskId
        )
        {
            var url = "/api/Tasks/TaskUploadUpload"; // TODO: determine if this is being used?

            var content = new MultipartFormDataContent
            {
                { new StringContent(memberFirmId.ToString()), "memberFirmId" },
                { new StringContent(clientId.ToString()), "clientId" },
                { new StringContent(path), "path" },
                { new StringContent(projectId.ToString()), "projectId" },
                { new StringContent(taskId.ToString()), "taskId" },
                { new StringContent(file.Length.ToString()), "fileSize" },
            };

            using (var stream = file.OpenReadStream())
            {
                var fileContent = new StreamContent(stream)
                {
                    Headers = { ContentType = new MediaTypeHeaderValue(file.ContentType) },
                };
                content.Add(fileContent, "file", file.FileName);

                var response = await _controlApiClient.UploadFileAsync<HttpResponseMessage>(
                    url,
                    content
                );

                if (response == null || !response.IsSuccessStatusCode)
                {
                    throw new InvalidOperationException("File upload failed.");
                }

                var responseBody = await response.Content.ReadAsStringAsync();
                var uploadResult = JsonSerializer.Deserialize<BDOUploadResult>(responseBody);

                if (uploadResult == null)
                {
                    throw new InvalidOperationException("Invalid response received from upload.");
                }

                return uploadResult;
            }
        }

        public async Task<bool> UploadUploadedDocumentAsync(BDOUploadDocumentRequest request)
        {
            // This sets the action item to "In Progress"
            // TODO: refactor to use a simpler endpoint for this purpose
            const string url = "/api/Tasks/OnTaskUploadUploaded";

            if (request == null)
            {
                throw new ArgumentNullException(nameof(request), "Request cannot be null.");
            }

            try
            {
                var response = await _controlApiClient.PostAsync<BDOUploadDocumentResponse>(
                    url,
                    request
                );
                return true;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error in UploadUploadedDocumentAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ResendEmailInvitationAsync(BDOReinviteUser request)
        {
            const string url = "/api/TeamManagement/ResendEmailInvitation";

            if (request == null)
            {
                throw new ArgumentNullException(nameof(request), "Request cannot be null.");
            }

            try
            {
                var response = await _controlApiClient.PostAsync<object[]>(url, request);
                return true;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error in ResendEmailInvitationAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<WelcomeMessage?> GetWelcomeMessageAsync(int memberFirmId, string culture)
        {
            var url = "/api/WelcomeMessages/GetWelcomeMessage";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "memberFirmId", memberFirmId.ToString() },
                { "culture", culture },
            };

            return await _controlApiClient.GetAsync<WelcomeMessage>(url, queryParams);
        }

        public async Task<BDOUser[]> GetProjectUsers(int clientId, int projectId)
        {
            var url =
                "/api/ClientSite/GetProjectUsers?groups=BDO Admin User&groups=BDO User&groups=BDO Reader&groups=Client Admin User&groups=Client User&groups=Client Reader";

            var queryParams = new Dictionary<string, StringValues>
            {
                { "memberFirmId", _controlApiConfig.MemberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
            };

            var response = await _controlApiClient.GetAsync<BDOPortalMember[]>(url, queryParams);

            if (response == null)
            {
                throw new InvalidOperationException("ProjectUsers not found");
            }

            BDOUser[] users = response
                .Select(x => new BDOUser
                {
                    UniqueUserId = x.UniqueUserId,
                    EmailAddress = x.EmailAddress,
                    UserType = x.UserGroupName,
                    DisplayName = x.DisplayName,
                    FirstName = x.FirstName,
                    LastName = x.LastName,
                })
                .ToArray();

            return users;
        }

        private async Task<BDOUser?> FetchUserProfileAsync(string uniqueUserId, int clientId)
        {
            var firmId = _controlApiConfig.MemberFirmId;
            var url = $"/api/UserProfiles/GetUserProfile";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "uniqueUserId", uniqueUserId },
                { "firmId", firmId.ToString() },
                { "clientId", clientId.ToString() },
            };

            try
            {
                var profile = await _controlApiClient.GetAsync<BDOUser>(url, queryParams);
                if (profile == null)
                {
                    Console.WriteLine($"User profile not found for uniqueUserId: {uniqueUserId}");
                    return null;
                }

                profile.UniqueUserId = uniqueUserId;
                //TODO: This needs to be updated by context

                //TODO: Change this to be the userType from BGP
                // profile.UserType = "Client User";

                return profile;
            }
            catch (Exception ex)
            {
                Console.WriteLine(
                    $"Error fetching user profile for uniqueUserId {uniqueUserId}: {ex.Message}"
                );
                return null;
            }
        }

        // TODO: align on different methods of fetching projects, could be cleaned up

        public async Task<BDOClientProjectResponse[]> GetClientProjectsAsync(
            int memberFirmId,
            int clientId
        )
        {
            var queryParams = new Dictionary<string, StringValues>
            {
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
            };

            return await _controlApiClient.GetAsync<BDOClientProjectResponse[]>(
                    "api/Projects/GetProjects",
                    queryParams
                ) ?? Array.Empty<BDOClientProjectResponse>();
        }

        public async Task<BDOPortalMember[]?> GetPortalUsers(int memberFirmId, int clientId)
        {
            var url =
                "/api/ClientSite/GetPortalUsers?groups=BDO Admin User&groups=BDO User&groups=BDO Reader&groups=Client Admin User&groups=Client User&groups=Client Reader";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "memberFirmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
            };
            var result =
                await _controlApiClient.GetAsync<BDOPortalMember[]?>(url, queryParams)
                ?? throw new InvalidOperationException("Portal Users not found");

            return result;
        }

        // TODO: Can we consolidate with GetProjectUsers?
        public async Task<BDOProjectMember[]?> GetProjectMembersAsync(
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            var url = "/api/People/GetProjectMembers";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
            };

            return await _controlApiClient.GetAsync<BDOProjectMember[]?>(url, queryParams)
                ?? throw new InvalidOperationException("Project members not found");
        }

        public async Task<bool> MarkActionItemAsPriority(BDOActionItemPriorityRequest request)
        {
            var url = "/api/Tasks/FlagTask";
            await _controlApiClient.PostAsync<string>(url, request);
            return true;
        }

        public async Task<bool> UnMarkActionItemAsPriority(BDOActionItemPriorityRequest request)
        {
            var url = "/api/Tasks/UnflagTask";
            await _controlApiClient.PostAsync<string>(url, request);
            return true;
        }

        public async Task<BDOActionItem[]> GetClientActionItems(int memberFirmId, int clientId)
        {
            var url = "/api/Tasks/GetClientTasks";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
            };

            try
            {
                // BGP 5.8 removed the ability to fetch tasks by project, determine where to filter on BE?
                var result =
                    await _controlApiClient.GetAsync<BDOActionItem[]>(url, queryParams) ?? [];

                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    $"Error fetching action items on client with ID: {clientId}",
                    ex
                );
            }
        }

        public async Task<Guid> AddCommentsToActionItem(BDOAddCommentRequest request)
        {
            var url = "/api/Tasks/CreateTaskComment";
            return await _controlApiClient.PostAsync<Guid>(url, request);
        }

        public async Task<bool> UpdateComment(BDOUpdateCommentRequest request)
        {
            var url = "/api/Tasks/UpdateTaskComment";
            try
            {
                await _controlApiClient.PostAsync<string>(url, request);
                return true;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<bool> DeleteComment(BDODeleteCommentRequest request)
        {
            var url = "/api/Tasks/DeleteTaskComment";
            try
            {
                await _controlApiClient.DeleteAsync<string>(url, request);
                return true;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<IEnumerable<BDOClientProjectResponse>> GetUserAccessForProjectsAsync(
            GetUserAccessForProjectsRequest request
        )
        {
            try
            {
                var allProjects = await GetClientProjectsAsync(request.FirmId, request.ClientId);
                if (!allProjects.Any())
                {
                    _logger.LogWarning("No projects found.");
                    return Enumerable.Empty<BDOClientProjectResponse>();
                }

                var tasks = allProjects.Select(async project =>
                {
                    try
                    {
                        var projectUsers = await GetProjectUsers(request.ClientId, project.Id);
                        var hasAccess = projectUsers.Any(user =>
                            string.Equals(
                                user.EmailAddress,
                                request.Email,
                                StringComparison.OrdinalIgnoreCase
                            )
                        );
                        return hasAccess ? project : null;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to get users for project");
                        return null;
                    }
                });

                var results = await Task.WhenAll(tasks);
                return results.Where(p => p != null)!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user access for projects");
                throw;
            }
        }

        public async Task<bool> EditUserAsync(EditUserRequest request)
        {
            const string endpoint = "/api/TeamManagement/EditProjectsUser";

            try
            {
                var response = await _controlApiClient.PostAsync<object[]>(endpoint, request);

                if (response != null && response.Length == 0)
                {
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error editing user project access: {ex.Message}");
                return false;
            }
        }

        public async Task<BDOComment[]> GetComments(
            int memberFirmId,
            int clientId,
            int projectId,
            string taskId
        )
        {
            var url = "/api/Tasks/GetTaskComments";

            var queryParams = new Dictionary<string, StringValues>
            {
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
                { "taskId", taskId },
            };
            try
            {
                return await _controlApiClient.GetAsync<BDOComment[]>(url, queryParams) ?? [];
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<BDODocumentDirectory?> GetDocumentsDirectoryAsync(
            int memberFirmId,
            int clientId,
            int projectId,
            string relativePath
        )
        {
            var url = "/api/Document/GetDocumentsDirectory";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "memberFirmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
                { "relativePath", relativePath },
            };

            return await _controlApiClient.GetAsync<BDODocumentDirectory?>(url, queryParams)
                ?? throw new InvalidOperationException("Document directory not found");
        }

        public async Task<BDODocumentItem?> GetDocumentDetails(
            int clientId,
            int projectId,
            string? driveItemId,
            string? path
        )
        {
            if (string.IsNullOrEmpty(driveItemId) && string.IsNullOrEmpty(path))
            {
                throw new ArgumentException("driveItemId or path must be provided");
            }

            var url = "/api/Document/GetDocumentDetails";

            var queryParams = new Dictionary<string, StringValues>
            {
                { "firmId", _controlApiConfig.MemberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
                { "driveItemId", driveItemId },
                { "path", path ?? "/" }, // Required by BGP, but superceded if driveItemId is present
            };

            // BGP endpoint throws 500 if driveItemId or path not found.
            // Catch this to return null instead.
            try
            {
                return await _controlApiClient.GetAsync<BDODocumentItem>(url, queryParams);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return null;
            }
        }

        public async Task<Guid> CreateActionItem(BDOActionItemRequest actionItem)
        {
            var url = "/api/Tasks/CreateTask";
            actionItem.MemberFirmId = _controlApiConfig.MemberFirmId;
            return await _controlApiClient.PostAsync<Guid>(url, actionItem);
        }

        public async Task UpdateActionItem(BDOUpdateActionItemRequest actionItem)
        {
            var url = "/api/Tasks/UpdateTask";
            actionItem.MemberFirmId = _controlApiConfig.MemberFirmId;
            await _controlApiClient.PostAsync<string?>(url, actionItem);
        }

        public async Task<BDOActionItem?> GetActionItem(Guid taskId, int clientId, int projectId)
        {
            var url = "/api/Tasks/GetTaskDetails";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "memberFirmId", _controlApiConfig.MemberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
                { "taskId", taskId.ToString() },
            };
            return await _controlApiClient.GetAsync<BDOActionItem?>(url, queryParams);
        }

        public async Task<bool> DeleteActionItem(
            Guid taskId,
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            var url = "/api/Tasks/RemoveTask";
            var payload = new
            {
                taskId,
                firmId = memberFirmId,
                clientId,
                projectId,
            };

            try
            {
                await _controlApiClient.DeleteAsync<object>(url, payload);
                return true;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<BDOActionItemOverview[]> GetActionItemOverview(
            int memberFirmId,
            int clientId
        )
        {
            var url = "/api/Tasks/GetMyPortalTasks";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
            };
            return await _controlApiClient.GetAsync<BDOActionItemOverview[]>(url, queryParams);
        }

        public async Task UpdateDocumentAccess(BDOManageAccessRequest request)
        {
            var url = "/api/Document/UpdatePermissions";
            await _controlApiClient.PostAsync<object>(url, request);
        }

        public async Task<bool> CopyDocumentToTask(CopyDocumentToActionItemRequest body)
        {
            var url = "/api/Document/CopyDocument";
            return await _controlApiClient.PostAsync<bool>(url, body);
        }

        public async Task<DocumentDirectoryResponse> GetDocumentDirectory(
            string path,
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            var url = "/api/Document/GetDocumentsDirectory";
            var queryParams = new Dictionary<string, StringValues>
            {
                { "memberFirmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
                { "relativePath", path },
            };
            return await _controlApiClient.GetAsync<DocumentDirectoryResponse>(url, queryParams);
        }

        public async Task<BDOFolder> CreateNewFolder(FolderCreationRequest request)
        {
            var queryParams = new Dictionary<string, StringValues>
            {
                { "firmId", request.MemberFirmId.ToString() },
                { "clientId", request.ClientId.ToString() },
                { "projectId", request.ProjectId.ToString() },
                { "path", request.Path },
                { "name", request.Name },
            };

            var url = QueryHelpers.AddQueryString("/api/document/CreateFolder", queryParams);

            var response = await _controlApiClient.PostAsync<BDOFolder>(url, null);

            if (response == null)
            {
                throw new Exception("Failed to create folder. The API returned null.");
            }

            return response;
        }

        public async Task<bool> CreateUploadsFolder(
            Guid taskId,
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            var url = "/api/Tasks/EnsureTaskFolderStructure";
            var body = new
            {
                firmId = memberFirmId,
                clientId,
                projectId,
                taskId,
                folderName = "Uploads",
            };

            return await _controlApiClient.PostAsync<bool>(url, body);
        }

        public async Task CompleteActionItem(BDOCompleteActionItemRequest request)
        {
            var url = "/api/Tasks/CompleteTask";
            var body = new
            {
                firmId = request.FirmId,
                clientId = request.ClientId,
                projectId = request.ProjectId,
                taskId = request.TaskId,
            };

            await _controlApiClient.PostAsync<object?>(url, body);
        }

        public async Task<bool> ReturnActionItem(BDOReturnActionItemRequest request)
        {
            var url = "/api/Tasks/ReturnTask";
            var body = new
            {
                firmId = request.FirmId,
                clientId = request.ClientId,
                projectId = request.ProjectId,
                extendedDueDate = request.ExtendedDueDate,
                taskId = request.TaskId,
                reason = request.Reason,
            };
            try
            {
                await _controlApiClient.PostAsync<object?>(url, body);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<BDORecycleBinDocument[]?> GetRecycleBinAsync(
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            var url = "/api/Document/GetRecycleBinItems";

            var queryParams = new Dictionary<string, StringValues>
            {
                { "memberFirmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
                { "rowLimit", "1000" },
                { "isAscending", "true" },
                { "itemState", "1" },
                { "orderBy", "3" },
                { "showOnlyMyItems", "false" },
            };

            return await _controlApiClient.GetAsync<BDORecycleBinDocument[]?>(url, queryParams);
        }

        public async Task<bool> DeleteRecycleBinItemsPermanently(
            BDODeleteRecycleBinItemPermanentlyRequest request
        )
        {
            var url = "/api/Document/DeleteRecycleBinItems";

            var queryParams = new Dictionary<string, StringValues>
            {
                { "memberFirmId", request.MemberFirmId.ToString() },
                { "clientId", request.ClientId.ToString() },
                { "projectId", request.ProjectId.ToString() },
            };

            try
            {
                await _controlApiClient.PostAsync<object?>(url, request.Files, queryParams);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                return false;
            }
        }

        public async Task MoveItemsToRecycleBin(BDOMoveToTrashRequest request)
        {
            var url = "/api/Document/Delete";
            try
            {
                await _controlApiClient.PostAsync<object?>(url, request);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
        }

        public async Task RestoreDocuments(
            int _clientId,
            int _memberFirmId,
            int _projectId,
            BDORestoreRequest request
        )
        {
            var url = "/api/Document/Restore";

            var queryParams = new Dictionary<string, StringValues>
            {
                { "memberFirmId", _memberFirmId.ToString() },
                { "clientId", _clientId.ToString() },
                { "projectId", _projectId.ToString() },
            };

            try
            {
                await _controlApiClient.PostAsync<object?>(url, request.Items, queryParams);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
        }

        public async Task<BDOTaskDetailsForDocumentViewResponse?> GetTaskDetailsForDocument(
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            var url = "/api/Tasks/GetTaskDetailsForDocumentView";

            var queryParams = new Dictionary<string, StringValues>
            {
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
                { "includeDeleted", "true" },
            };
            return await _controlApiClient.GetAsync<BDOTaskDetailsForDocumentViewResponse>(
                    url,
                    queryParams
                ) ?? null;
        }

        public async Task<ProjectNameValidationResult> ValidateClientProjectName(
            string projectName,
            string siteType
        )
        {
            var url = "/api/ClientManagement/ValidateClientProjectName";
            ValidateProjectNameRequest body = new ValidateProjectNameRequest();
            body.Name = projectName;
            body.SiteType = siteType;
            try
            {
                return await _controlApiClient.PostAsync<ProjectNameValidationResult>(url, body);
            }
            catch (Exception ex)
            {
                var errorResponse = new ProjectNameValidationResult();
                errorResponse.IsValid = false;
                errorResponse.ValidationMessage = ex.Message;
                return errorResponse;
            }
        }

        public async Task<string> CheckProjectSiteExists(
            int memberFirmId,
            int clientId,
            string projectName,
            bool confidential = false,
            bool prospect = false,
            bool isInternal = false
        )
        {
            var url = "/api/ClientManagement/CheckIfProjectSiteExists";
            Dictionary<string, StringValues> parameters = new Dictionary<string, StringValues>
            {
                { "mfId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectName", projectName },
                { "confidential", confidential.ToString() },
                { "prospect", prospect.ToString() },
                { "isInternal", isInternal.ToString() },
            };

            try
            {
                return await _controlApiClient.PostAsync<string>(url, parameters); // returns "" or String
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                return ""; // TODO: determine correct error handling
            }
        }

        public async Task<bool> RenameProject(
            int memberFirmId,
            int clientId,
            int projectId,
            string projectName
        )
        {
            var url = "/api/Projects/RenameProject";

            var payload = new
            {
                firmId = memberFirmId.ToString(),
                clientId = clientId.ToString(),
                projectId,
                name = projectName,
            };

            try
            {
                await _controlApiClient.PostAsync<object?>(url, payload, null);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<BDOTaskGroup[]> GetTaskGroups(
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            var url = "/api/Tasks/GetTaskGroups";
            var parameters = new Dictionary<string, StringValues>
            {
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
            };
            return await _controlApiClient.GetAsync<BDOTaskGroup[]>(url, parameters);
        }

        public async Task<Guid> CreateTaskGroup(CreateTaskGroupRequest body)
        {
            var url = "/api/Tasks/CreateTaskGroup";
            return await _controlApiClient.PostAsync<Guid>(url, body);
        }

        public async Task<bool> OnSignatureDocumentsUploaded(
            BDOAssociateDocumentToActionItemRequest request
        )
        {
            var url = "/api/Tasks/OnSignatureDocumentsUploaded";
            try
            {
                await _controlApiClient.PostAsync<int?>(url, request);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                return false;
            }
        }

        public async Task<string> SignActionItem(BDOSignActionItemRequest request)
        {
            var url = "/api/Tasks/SignTask";
            return await _controlApiClient.PostAsync<string>(url, request);
        }

        public async Task<bool> RenameDocument(BDORenameDocumentRequest request)
        {
            var url = "/api/Document/Rename";

            var payload = new
            {
                request.MemberFirmId,
                request.ClientId,
                request.ProjectId,
                request.ItemId,
                request.Name,
            };

            try
            {
                await _controlApiClient.PostAsync<object?>(url, payload);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<BDOCurrentUserRoles?> GetCurrentUserRoles(
            GetCurrentUserRolesRequest request
        )
        {
            var url = "/api/Users/<USER>";
            var parameters = new Dictionary<string, StringValues>
            {
                { "memberFirmId", _controlApiConfig.MemberFirmId.ToString() },
                { "clientId", request.ClientId.ToString() },
            };

            if (request.ProjectId.HasValue)
            {
                parameters.Add("projectId", request.ProjectId.Value.ToString());
            }

            return await _controlApiClient.GetAsync<BDOCurrentUserRoles>(url, parameters);
        }

        public async Task RemoveUserRoles(RemoveBgpAccessRequest request)
        {
            request.FirmId = _controlApiConfig.MemberFirmId;
            var url = "/api/TeamManagement/RemoveUser";
            await _controlApiClient.PostAsync<object?>(url, request);
        }

        public async Task AddUserRoles(AddBgpAccessRequest request)
        {
            var url = "/api/TeamManagement/AddUsers";
            await _controlApiClient.PostAsync<object?>(url, request);
        }

        public async Task<BDOPeopleSearchResponse[]> PeopleSearch(
            string query,
            string? clientId = null,
            string? accessType = null
        )
        {
            var baseUrl = "/api/People/Search";
            var memberFirmId = _controlApiConfig.MemberFirmId;
            if (memberFirmId == 0)
                throw new CoreException("MemberFirm is not defined");
            var queryParams = new Dictionary<string, string?>
            {
                { "memberFirmId", memberFirmId.ToString() },
            };
            if (!string.IsNullOrWhiteSpace(query))
                queryParams.Add("query", query);
            if (!string.IsNullOrWhiteSpace(clientId))
                queryParams.Add("clientId", clientId);
            if (!string.IsNullOrWhiteSpace(accessType))
                queryParams.Add("accessType", accessType);
            var url = QueryHelpers.AddQueryString(baseUrl, queryParams);
            return await _controlApiClient.GetAsync<BDOPeopleSearchResponse[]>(url) ?? [];
        }

        public async Task<BDOUser?> GetBdoUserByEmail(string email, int clientId)
        {
            var baseUrl = "/api/Users/<USER>";
            var memberFirmId = _controlApiConfig.MemberFirmId;
            if (memberFirmId == 0)
                throw new CoreException("MemberFirm is not defined");
            var parameters = new Dictionary<string, StringValues>
            {
                { "memberFirmId", memberFirmId.ToString() },
                { "email", email },
            };
            var response = await _controlApiClient.GetAsync<JsonElement>(baseUrl, parameters);

            var userId = response.GetProperty("uniqueUserId").GetString();
            if (string.IsNullOrWhiteSpace(userId))
                return null;

            var user = await FetchUserProfileAsync(userId, clientId); // needs client id
            user = user ?? new BDOUser { UniqueUserId = userId, EmailAddress = email.Trim() };
            return user;
        }

        public async Task<BDOGetAccessibleProjectsResponse?> GetAccessibleProjects(int clientId)
        {
            var baseUrl = "/api/Projects/GetProjects";
            var memberFirmId = _controlApiConfig.MemberFirmId;
            var parameters = new Dictionary<string, StringValues>
            {
                { "firmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
            };
            return await _controlApiClient.GetAsync<BDOGetAccessibleProjectsResponse>(
                baseUrl,
                parameters
            );
        }

        public async Task<BDOPerson[]> GetAvailableUsersToAssignAccessForDocument(
            BDOAvailableUsersToAssignAccessRequest request
        )
        {
            var url = "/api/Document/GetAvailableUsersToAssignAccess";
            var parameters = new Dictionary<string, StringValues>
            {
                { "memberFirmId", request.MemberFirmId.ToString() },
                { "clientId", request.ClientId.ToString() },
            };

            if (request.ProjectId.HasValue)
            {
                parameters.Add("projectId", request.ProjectId.Value.ToString());
            }

            if (request.ItemId.HasValue)
            {
                parameters.Add("itemId", request.ItemId.Value.ToString());
            }
            if (!string.IsNullOrEmpty(request.NewFolderPath))
            {
                parameters.Add("newFolderPath", request.NewFolderPath);
            }
            try
            {
                return await _controlApiClient.GetAsync<BDOPerson[]>(url, parameters) ?? [];
            }
            catch (Exception e)
            {
                this._logger.LogWarning("failed to get available assignees");
            }
            return null;
        }

        public async Task<bool> UpdateItemPermissions(BDOUpdateFolderPermissionsRequest request)
        {
            var url = "/api/Document/UpdatePermissions";
            await _controlApiClient.PostAsync<object?>(url, request);
            return true;
        }

        public async Task<dynamic?> GetItemPermissions(
            int memberFirmId,
            int clientId,
            int projectId,
            int itemId
        )
        {
            var url = "/api/Document/GetPermissions";
            var parameters = new Dictionary<string, StringValues>
            {
                { "memberFirmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
                { "itemId", itemId.ToString() },
            };
            return await _controlApiClient.GetAsync<dynamic>(url, parameters);
        }

        public async Task<BDOSearchUser[]> GetUsersFromSiteGroup(
            int memberFirmId,
            int clientId,
            int projectId
        )
        {
            var url = "/api/Users/<USER>";
            var parameters = new Dictionary<string, StringValues>
            {
                { "memberFirmId", memberFirmId.ToString() },
                { "clientId", clientId.ToString() },
                { "projectId", projectId.ToString() },
            };
            return await _controlApiClient.GetAsync<BDOSearchUser[]>(url, parameters);
        }

        public async Task<BDOSearchUser[]> GetMemberFirmUsers(int memberFirmId)
        {
            var url = "/api/ClientManagement/GetMemberFirmUsers";
            var parameters = new Dictionary<string, StringValues>
            {
                { "mfId", memberFirmId.ToString() },
            };
            return await _controlApiClient.GetAsync<BDOSearchUser[]>(url, parameters);
        }

        public async Task<dynamic> UpdateMyUserProfile(BDOUpdateMyUserProfileRequest request)
        {
            var url = "/api/UserProfiles/UpdateMyUserProfile";
            return await _controlApiClient.PostAsync<dynamic>(url, request);
        }

        public async Task ExportAllDocumentsToApt(int memberFirmId, int clientId, int projectId)
        {
            // Call the external BGP APT API directly - NOTE: Different endpoint for "all documents"
            var aptApiUrl =
                $"https://api.bdo.global/apt-api-acc-can/api/Projects/SendDocumentsToApt?memberFirmId={memberFirmId}&clientId={clientId}&projectId={projectId}";

            using var httpClient = new HttpClient();
            var response = await httpClient.PostAsync(
                aptApiUrl,
                new StringContent("", System.Text.Encoding.UTF8, "application/json")
            );

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException(
                    $"APT API call failed with status {response.StatusCode}: {errorContent}"
                );
            }
        }

        public async Task ExportSelectedDocumentsToApt(
            int memberFirmId,
            int clientId,
            int projectId,
            List<int> files,
            List<int> folders
        )
        {
            // Call the external BGP APT API directly
            var aptApiUrl =
                "https://api.bdo.global/apt-api-acc-can/api/Projects/SendSelectedDocumentsToApt";

            var payload = new
            {
                memberFirmId = memberFirmId,
                clientId = clientId,
                projectId = projectId,
                files = files,
                folders = folders,
            };

            using var httpClient = new HttpClient();
            var jsonContent = new StringContent(
                System.Text.Json.JsonSerializer.Serialize(payload),
                System.Text.Encoding.UTF8,
                "application/json"
            );

            var response = await httpClient.PostAsync(aptApiUrl, jsonContent);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException(
                    $"APT API call failed with status {response.StatusCode}: {errorContent}"
                );
            }
        }
    }
}
