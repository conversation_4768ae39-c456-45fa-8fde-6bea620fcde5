using BCP.Core.BGP.ControlAPI.Models;
using BCP.Core.Document.Models;
using BCP.Core.Modules.BGP.ControlAPI.Models;
using Microsoft.AspNetCore.Http;

namespace BCP.Core.Document;

public interface IDocumentService
{
    Task<BDOUploadDocument> UploadDocumentAsync(
        IFormFile file,
        int memberFirmId,
        int clientId,
        string path,
        int projectId,
        int? folderItemId
    );
    Task<byte[]> DownloadFileAsync(string filePath);
    Task<string> CreateDownloadFilesOrFolderAsync(DocumentDownloadRequest request);
    Task<Stream> DownloadFilesOrFolderAsync(string downloadKey);
    Task<DocumentsResponse?> GetFolderContentAsync(
        string path,
        int memberFirmId,
        int clientId,
        int? projectId,
        string? filter
    );

    Task<DocumentResponse?> GetDocumentDetails(
        int clientId,
        int projectId,
        string? driveItemId,
        string? path
    );

    Task<BDOPerson[]> GetAvailableUsersToAssignAccess(
        BDOAvailableUsersToAssignAccessRequest request
    );
    Task<BDOFolder> CreateNewFolder(FolderCreationRequest request);
    Task<BDORecycleBinDocument[]?> GetRecycleBinAsync(
        int memberFirmId,
        int clientId,
        int projectId
    );
    Task<bool> DeleteRecycleBinItemsPermanently(BDODeleteRecycleBinItemPermanentlyRequest request);

    Task MoveItemsToRecycleBin(BDOMoveToTrashRequest request);
    Task RestoreDocuments(
        int _clientId,
        int _memberFirmId,
        int _projectId,
        BDORestoreRequest request
    );
    Task<bool> AssociateActionItemToDocument(
        string actionItemId,
        int clientId,
        int projectId,
        BDOUploadDocument document
    );

    void ValidateFileForUpload(IFormFile file);

    Task<AptExportValidationResponse> GetAptExportValidationAsync(
        int memberFirmId,
        int clientId,
        int projectId,
        List<AptExportDocumentInfo> documents
    );

    Task<AptExportResponse> ExportAllDocumentsToAptAsync(
        int memberFirmId,
        int clientId,
        int projectId
    );

    Task<AptExportResponse> ExportSelectedDocumentsToAptAsync(
        int memberFirmId,
        int clientId,
        int projectId,
        List<int> files,
        List<int> folders
    );
}
