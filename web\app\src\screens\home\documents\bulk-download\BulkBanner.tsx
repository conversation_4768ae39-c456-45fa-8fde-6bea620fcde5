import { Button, ButtonSizeEnum, ButtonTypeEnum, Icon } from "@bcp/uikit";
import styles from "./bulkContainer.module.css";
import { defaultProps } from "~/default.spec";
import { useTranslation } from "react-i18next";

interface BulkBannerProps extends defaultProps {
  selectedRows: string[];
  setSelectedRows: React.Dispatch<React.SetStateAction<string[]>>;
  shouldHideMoveButton: boolean;
  onClickDownload: () => void;
  onClickMove: () => void;
  onClickMoveToBin: () => void;
  onClickExportToApt?: () => void;
  isLinkedToApt?: boolean;
}

export const BulkBanner: React.FC<BulkBannerProps> = ({
  selectedRows,
  setSelectedRows,
  dataTestId = "uikit-bulkBanner",
  ariaLabel = `${selectedRows.length} selected for bulk download`,
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
  role = "banner",
  shouldHideMoveButton,
  onClickDownload,
  onClickMove,
  onClickMoveToBin,
  onClickExportToApt,
  isLinkedToApt = false,
}) => {
  const { t } = useTranslation("documents");

  return (
    <div
      className={styles.container}
      data-testid={dataTestId}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-labelledby={ariaLabelledBy}
      tabIndex={tabIndex}
      role={role}
    >
      <div className={styles.leftSection}>
        <button
          aria-label={t("unselect-all", { count: selectedRows.length })}
          className={styles.closeIconContainer}
          onClick={() => setSelectedRows([])}
        >
          <Icon iconName="close-icon" altText="Close" size={16} />
        </button>
        <span className={styles.selectedText}>
          {t("items-selected", { count: selectedRows.length })}
        </span>
      </div>
      <div className={styles.buttonsContainer}>
        {isLinkedToApt && selectedRows.length > 0 && (
          <div className={styles.buttonWrapper}>
            <Button
              id="send-to-apt-button"
              rightIconName="send-icon"
              label={t("send-to-apt")}
              withRightIcon
              onClick={() => {
                onClickExportToApt?.();
              }}
              type={ButtonTypeEnum.tertiary}
              size={ButtonSizeEnum.icon}
              dataTestId="sendToAptButton"
              ariaLabel={`Send ${selectedRows.length} items to APT`}
            />
          </div>
        )}
        <div className={styles.buttonWrapper}>
          <Button
            id="bulk-download-button"
            rightIconName="arrow-download"
            label={t("download-selected")}
            withRightIcon
            onClick={() => {
              onClickDownload();
            }}
            type={ButtonTypeEnum.tertiary}
            size={ButtonSizeEnum.icon}
            dataTestId={"downloadFilesButton"}
            ariaLabel={`Download ${selectedRows.length} files`}
          />
        </div>
        {!shouldHideMoveButton && (
          <div className={styles.buttonWrapper}>
            <Button
              id="bulk-move-button"
              rightIconName="folder-arrow-right"
              label={t("move-cta")}
              withRightIcon
              onClick={async () => {
                await onClickMove();
              }}
              type={ButtonTypeEnum.tertiary}
              size={ButtonSizeEnum.icon}
              dataTestId={"moveFilesButton"}
              ariaLabel={`Move ${selectedRows.length} files`}
            />
          </div>
        )}
        {!shouldHideMoveButton && (
          <div className={styles.buttonWrapper}>
            <Button
              id="bulk-recycle-button"
              rightIconName="move-document-to-recycle-bin-icon"
              label={t("move-to-recycle-bin")}
              withRightIcon
              onClick={async () => {
                await onClickMoveToBin();
              }}
              type={ButtonTypeEnum.tertiary}
              size={ButtonSizeEnum.icon}
              dataTestId={"deleteItemsButton"}
              ariaLabel={`Deleting ${selectedRows.length} items`}
              isWarning
            />
          </div>
        )}
      </div>
    </div>
  );
};
